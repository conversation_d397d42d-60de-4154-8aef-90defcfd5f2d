import yaml
import os
from typing import Any, Dict, Optional

class Config:
    def __init__(self, config_path: str = "config.yaml", default_config: Optional[Dict[str, Any]] = None):
        self.config_path = config_path
        self.default_config = default_config or {}
        self._data = self._load_or_create()
    
    def _load_or_create(self) -> Dict[str, Any]:
        """Load config hoặc tạo mới với config mặc định"""
        if os.path.exists(self.config_path):
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        
        # Tạo file mới với config mặc định
        self._save_data(self.default_config)
        return self.default_config.copy()
    
    def _save_data(self, data: Dict[str, Any]) -> None:
        """Lưu data ra file"""
        os.makedirs(os.path.dirname(self.config_path) or '.', exist_ok=True)
        with open(self.config_path, 'w', encoding='utf-8') as f:
            yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
    
    def get(self, key: str, default: Any = None) -> Any:
        """Lấy giá trị theo key (hỗ trợ nested: 'app.name')"""
        value = self._data
        for k in key.split('.'):
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value
    
    def set(self, key: str, value: Any) -> None:
        """Set giá trị theo key"""
        keys = key.split('.')
        data = self._data
        
        for k in keys[:-1]:
            data = data.setdefault(k, {})
        data[keys[-1]] = value
    
    def save(self) -> None:
        """Lưu config ra file"""
        self._save_data(self._data)
    
    def reload(self) -> None:
        """Reload config từ file"""
        self._data = self._load_or_create()
