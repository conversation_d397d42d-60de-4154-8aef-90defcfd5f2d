import asyncio
import multiprocessing as mp

from camoufox.async_api import Async<PERSON>amoufox
from browserforge.fingerprints import Screen

from config import Config

class Bot:
    def __init__(self):

        self.default_config = {
            'threads': 1,
            'proxy': {
                'status': False,
                'type': 'http',
                'data': 'proxy.txt'
            }
        }
        self.config = Config(default_config=self.default_config)

    async def run(self):
        try:
            constrains = Screen(max_width=1920, max_height=1080)
            async with AsyncCamoufox(
                headless=False,
                os=["windows", "macos"],
                screen=constrains
                ) as browser:

                page = await browser.new_page()
                await page.goto("https://fingerprint.com")
                await page.wait_for_load_state(state="domcontentloaded")
                await page.wait_for_load_state("networkidle")
                await asyncio.sleep(30)
        
        except Exception as e:
            print(f"[!] Error: {e}")


async def main():
    bot = Bot()
    await bot.run()




if __name__ == '__main__':
    asyncio.run(main())